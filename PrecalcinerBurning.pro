QT += core widgets quick quickcontrols2 charts network

# 根据构建类型决定是否显示控制台
CONFIG += c++11
# 静态链接选项（用于更好的兼容性）
CONFIG += static
CONFIG(debug, debug|release) {
    CONFIG += console debug
    # 启用硬件数据采集和调试输出
    DEFINES += ENABLE_HARDWARE_DATA
    DEFINES += DEBUG
} else {
    # Release模式下不显示控制台，禁用调试输出
    DEFINES += ENABLE_HARDWARE_DATA
    DEFINES += QT_NO_DEBUG_OUTPUT
}

# Windows specific libraries
win32 {
    LIBS += -lws2_32 -lole32 -loleaut32 -luuid -lpsapi
    # 设置Windows目标版本为Windows 10 (提升性能和稳定性)
    DEFINES += WINVER=0x0A00 _WIN32_WINNT=0x0A00
    # 启用现代Windows API优化
    DEFINES += WIN32_LEAN_AND_MEAN
    # 使用Windows 10 API
    QMAKE_CXXFLAGS += -D_WIN32_WINNT=0x0A00
    QMAKE_CFLAGS += -D_WIN32_WINNT=0x0A00
    # 链接器设置，针对Windows 10优化
    QMAKE_LFLAGS += -Wl,--major-subsystem-version,10 -Wl,--minor-subsystem-version,0

    # 32位内存优化设置 - 保守且稳定的配置
    QMAKE_LFLAGS += -Wl,--large-address-aware    # 启用大地址空间感知
    # 移除显式的栈和堆大小设置，使用系统默认值以避免启动错误

    # 编译器优化设置
    QMAKE_CXXFLAGS_RELEASE += -O2
    QMAKE_CXXFLAGS_DEBUG += -O1
}

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# For Qt 5.12, use 0x051200 instead of 0x060000
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x051200    # disables all the APIs deprecated before Qt 5.12.0

TARGET = PrecalcinerBurning
TEMPLATE = app

# Source files
SOURCES += \
    main.cpp \
    monitorwindow.cpp \
    monitoring_datasource.cpp \
    datascreen.cpp \
    boiler.cpp \
    config_manager.cpp \
    configmanager_qml.cpp \
    smoke_analyzer_comm.cpp \
    csvfile.cpp \
    csvreader.cpp \
    redisclient.cpp \
    dcsopc.cpp \
    DACLTSDK.cpp \
    parameter_adjustment.cpp \
    parameter_adjustment_qml.cpp \
    systemtray.cpp

# Header files
HEADERS += \
    monitorwindow.h \
    monitoring_datasource.h \
    datascreen.h \
    boiler.h \
    config_manager.h \
    configmanager_qml.h \
    smoke_analyzer_comm.h \
    csvfile.h \
    csvreader.h \
    redisclient.h \
    dcsopc.h \
    DACLTSDK.h \
    parameter_adjustment.h \
    parameter_adjustment_qml.h \
    systemtray.h

# UI files
# FORMS += \

# Resources
RESOURCES += qml.qrc

# Windows icon resource (使用UTF-8编码版本)
win32:RC_FILE = app_icon_en.rc

# 资源编译器设置
win32 {
    # 设置资源编译器使用UTF-8编码
    QMAKE_RC += -c 65001
    # 确保使用正确的资源编译器
    RC_INCLUDEPATH += $$PWD
}

# Translation files
TRANSLATIONS += PrecalcinerBurning_zh_CN.ts

# Other files
OTHER_FILES += \
    config.ini \
    Windows串口使用说明.md \
    数据采集集成说明.md

# Copy config files to output directory
win32 {
    CONFIG(debug, debug|release) {
        DESTDIR = $$OUT_PWD/debug
    } else {
        DESTDIR = $$OUT_PWD/release
    }

    # Copy data folder (create empty directory if needed)
    data.path = $$DESTDIR/data
    data.files = $$PWD/data/*
    INSTALLS += data

    # Copy config file
    config.path = $$DESTDIR
    config.files = $$PWD/config.ini
    INSTALLS += config

    # Create necessary directories and copy files
    QMAKE_POST_LINK += $$quote(if not exist \"$$shell_path($$DESTDIR)\" mkdir \"$$shell_path($$DESTDIR)\")
    QMAKE_POST_LINK += $$quote(&& if not exist \"$$shell_path($$DESTDIR/data)\" mkdir \"$$shell_path($$DESTDIR/data)\")

    # Copy config file (essential for startup)
    QMAKE_POST_LINK += $$quote(&& copy /Y \"$$shell_path($$PWD/config.ini)\" \"$$shell_path($$DESTDIR/)\")
}

unix {
    # Copy config files for Unix systems
    # Copy data folder
    data.path = $$OUT_PWD/data
    data.files = $$PWD/data/*
    INSTALLS += data

    # Copy config file
    config.path = $$OUT_PWD
    config.files = $$PWD/config.ini
    INSTALLS += config

    # For immediate copying during build
    QMAKE_POST_LINK += $$quote(cp -r $$PWD/data $$OUT_PWD/)
    QMAKE_POST_LINK += $$quote(cp $$PWD/config.ini $$OUT_PWD/)
}

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
