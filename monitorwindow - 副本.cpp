#include "monitorwindow.h"
#include <QTimer>

MonitorWindow::MonitorWindow(QObject *parent)
    : QObject(parent)
    , m_dataSource(new MonitoringDataSource(this))
{
}

void MonitorWindow::startMonitoring()
{
    m_dataSource->startMonitoring();
}

void MonitorWindow::stopMonitoring()
{
    m_dataSource->stopMonitoring();
}

void MonitorWindow::clearAllData()
{
    m_dataSource->clearData();
}

void MonitorWindow::switchBoilerAsync(const QString &boilerName)
{
    // 使用单次定时器将锅炉切换操作推迟到下一个事件循环
    // 这样可以避免阻塞UI线程
    QTimer::singleShot(0, [this, boilerName]() {
        m_dataSource->setCurrentBoiler(boilerName);
    });
}
